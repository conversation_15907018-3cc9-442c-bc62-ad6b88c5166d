# Quick Setup Guide

## 🚀 Get Started in 30 Seconds!

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Run the Application
```bash
npm run dev
```

### Step 3: Open Your Browser
Visit [http://localhost:3000](http://localhost:3000) to see your tornado tracker!

**That's it!** No external accounts, API keys, or configuration needed! 🎉

## 🎯 What You'll See

- **Main Map**: Interactive OpenStreetMap of America ready for tornado reports
- **Report Button**: Click "Report Tornado" to test the upload functionality
- **Admin Panel**: Visit `/admin` to moderate reports
- **Sample Data**: Pre-loaded with example tornado reports to demonstrate functionality

## 🧪 Test the Application

1. **Report a Tornado**:
   - Click anywhere on the map
   - Fill out the form with test data
   - Upload a sample image/video (stored as base64 locally)
   - Submit the report

2. **Moderate Reports**:
   - Go to `/admin`
   - Review the pending report
   - Approve or reject it
   - Check the main map to see approved reports

## 🔧 Troubleshooting

**Map Not Loading**: Refresh the page - Leaflet sometimes needs a moment to initialize

**Data Not Persisting**: Data is stored in localStorage - clearing browser data will reset the app

**Large File Uploads**: Files are converted to base64, so very large files may impact performance

## 📱 Features Overview

- ✅ Interactive OpenStreetMap focused on America
- ✅ Click-to-report tornado activity
- ✅ Photo and video uploads (stored locally as base64)
- ✅ Email-based reporting system
- ✅ Admin moderation dashboard
- ✅ Local data persistence with localStorage
- ✅ Responsive design for mobile/desktop
- ✅ Zero external dependencies
- ✅ Complete privacy - no data leaves your browser
- ✅ Works offline after initial load

## 🚀 Ready for Production

To deploy:
1. Push to GitHub
2. Connect to Vercel (or any static hosting)
3. Deploy!

No environment variables or external services needed!

Your open-source tornado tracking application is now ready to help communities stay informed about severe weather across America! 🌪️
