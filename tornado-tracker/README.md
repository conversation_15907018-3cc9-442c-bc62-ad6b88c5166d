# 🌪️ Tornado Tracker - America (Open Source)

A comprehensive tornado tracking application for America with photo/video upload capabilities and moderation system. **100% open-source with no external dependencies or API keys required!**

## Features

- **Interactive Map**: Focused on America with tornado tracking capabilities using OpenStreetMap
- **Media Upload**: Users can upload photos and videos with map pin placement
- **Moderation System**: Admin interface to approve/reject uploaded content
- **Local Storage**: All data stored locally in browser - no external database required
- **Responsive Design**: Works on desktop and mobile devices
- **Zero Configuration**: Works out of the box with no setup required

## Technology Stack

- **Frontend**: Next.js 15 with React and TypeScript
- **Database**: Local browser storage (localStorage)
- **Map**: Leaflet with OpenStreetMap tiles
- **Styling**: Tailwind CSS
- **Icons**: Lucide React

## Setup Instructions

### 1. Prerequisites

- Node.js 18+ installed
- That's it! No external accounts or API keys needed 🎉

### 2. <PERSON><PERSON> and Install

```bash
git clone <your-repo-url>
cd tornado-tracker
npm install
```

### 3. Run the Application

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

**That's it!** The application works immediately with no configuration required.

## Usage

### For Users
1. **View Tornado Reports**: Browse the map to see approved tornado reports
2. **Report Tornado Activity**: Click anywhere on the map to report tornado activity
3. **Upload Media**: Add photos or videos to your reports
4. **Track Status**: Reports are moderated before appearing publicly

### For Administrators
1. **Access Admin Panel**: Visit `/admin` to access the moderation dashboard
2. **Review Reports**: View pending reports with all details and media
3. **Moderate Content**: Approve or reject reports and associated media
4. **Manage Status**: Track approved and rejected reports

## Database Schema

### tornado_reports
- `id`: UUID (Primary Key)
- `lat`: Decimal (Latitude)
- `lng`: Decimal (Longitude)
- `title`: String (Report title)
- `description`: Text (Report description)
- `status`: Enum ('pending', 'approved', 'rejected')
- `created_at`: Timestamp
- `user_id`: UUID (Foreign Key)
- `user_email`: String (Reporter email)

### media_uploads
- `id`: UUID (Primary Key)
- `report_id`: UUID (Foreign Key to tornado_reports)
- `file_url`: String (Supabase Storage URL)
- `file_type`: Enum ('image', 'video')
- `status`: Enum ('pending', 'approved', 'rejected')
- `created_at`: Timestamp
- `user_id`: UUID (Foreign Key)

## Security Features

- **Row Level Security (RLS)**: Implemented on all tables
- **File Upload Limits**: 50MB per file limit
- **Content Moderation**: All reports require approval
- **Geographic Restrictions**: Map bounded to America only
- **Input Validation**: All forms include proper validation

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Other Platforms
The application can be deployed to any platform that supports Next.js applications.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please open an issue in the GitHub repository or contact the development team.
