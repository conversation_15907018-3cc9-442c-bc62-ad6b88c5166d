'use client'

import { useEffect, useState } from 'react'
import dynamic from 'next/dynamic'
import { TornadoReport, MediaUpload, localDB, initializeSampleData } from '@/lib/storage'

// Dynamically import Leaflet components to avoid SSR issues
const MapContainer = dynamic(() => import('react-leaflet').then(mod => mod.MapContainer), { ssr: false })
const TileLayer = dynamic(() => import('react-leaflet').then(mod => mod.TileLayer), { ssr: false })
const Marker = dynamic(() => import('react-leaflet').then(mod => mod.Marker), { ssr: false })
const Popup = dynamic(() => import('react-leaflet').then(mod => mod.Popup), { ssr: false })
const useMapEvents = dynamic(() => import('react-leaflet').then(mod => mod.useMapEvents), { ssr: false })

interface MapProps {
  onLocationSelect?: (lat: number, lng: number) => void
  selectedLocation?: { lat: number; lng: number } | null
}

// Custom hook for handling map clicks
function MapClickHandler({ onLocationSelect }: { onLocationSelect?: (lat: number, lng: number) => void }) {
  useMapEvents({
    click: (e) => {
      if (onLocationSelect) {
        onLocationSelect(e.latlng.lat, e.latlng.lng)
      }
    }
  })
  return null
}

export default function Map({ onLocationSelect, selectedLocation }: MapProps) {
  const [reports, setReports] = useState<TornadoReport[]>([])
  const [mediaUploads, setMediaUploads] = useState<MediaUpload[]>([])
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    initializeSampleData()
    loadReports()
  }, [])

  const loadReports = () => {
    const allReports = localDB.getReports()
    const approvedReports = allReports.filter(report => report.status === 'approved')
    const allMedia = localDB.getMedia()
    const approvedMedia = allMedia.filter(media => media.status === 'approved')

    setReports(approvedReports)
    setMediaUploads(approvedMedia)
  }

  const getReportMedia = (reportId: string) => {
    return mediaUploads.filter(media => media.report_id === reportId)
  }

  const createCustomIcon = () => {
    if (typeof window === 'undefined') return null

    const L = require('leaflet')
    return L.divIcon({
      html: '<div style="background-color: #dc2626; width: 30px; height: 30px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; font-size: 16px;">🌪️</div>',
      className: 'tornado-marker',
      iconSize: [30, 30],
      iconAnchor: [15, 15]
    })
  }

  const createSelectedLocationIcon = () => {
    if (typeof window === 'undefined') return null

    const L = require('leaflet')
    return L.divIcon({
      html: '<div style="background-color: #ef4444; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
      className: 'selected-location-marker',
      iconSize: [20, 20],
      iconAnchor: [10, 10]
    })
  }

  if (!isClient) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100" style={{ minHeight: '400px' }}>
        <div className="text-center p-8">
          <div className="text-6xl mb-4">🌪️</div>
          <h3 className="text-xl font-bold text-gray-800 mb-2">Loading Map...</h3>
          <p className="text-gray-600">Initializing open-source tornado tracker</p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full" style={{ minHeight: '400px' }}>
      <MapContainer
        center={[39.8283, -98.5795]} // Center of USA
        zoom={4}
        style={{ height: '100%', width: '100%' }}
        maxBounds={[
          [15, -180], // Southwest coordinates (Alaska included)
          [72, -50]   // Northeast coordinates
        ]}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />

        <MapClickHandler onLocationSelect={onLocationSelect} />

        {/* Tornado report markers */}
        {reports.map((report) => {
          const reportMedia = getReportMedia(report.id)
          return (
            <Marker
              key={report.id}
              position={[report.lat, report.lng]}
              icon={createCustomIcon()}
            >
              <Popup>
                <div className="p-2 max-w-sm">
                  <h3 className="font-bold text-lg mb-2">{report.title}</h3>
                  <p className="text-gray-600 mb-3">{report.description}</p>
                  <p className="text-xs text-gray-500 mb-3">
                    Reported: {new Date(report.created_at).toLocaleDateString()}
                  </p>
                  {reportMedia.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-semibold">Media:</h4>
                      {reportMedia.map(m => (
                        <div key={m.id}>
                          {m.file_type === 'image' ? (
                            <img
                              src={m.file_url}
                              alt="Tornado photo"
                              className="w-full h-32 object-cover rounded"
                            />
                          ) : (
                            <video
                              src={m.file_url}
                              controls
                              className="w-full h-32 rounded"
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </Popup>
            </Marker>
          )
        })}

        {/* Selected location marker */}
        {selectedLocation && (
          <Marker
            position={[selectedLocation.lat, selectedLocation.lng]}
            icon={createSelectedLocationIcon()}
          />
        )}
      </MapContainer>
    </div>
  )
}
