'use client'

import { useState } from 'react'
import { X, Upload, MapPin } from 'lucide-react'
import { localDB } from '@/lib/storage'

interface UploadModalProps {
  isOpen: boolean
  onClose: () => void
  selectedLocation: { lat: number; lng: number } | null
  onSuccess: () => void
}

export default function UploadModal({ isOpen, onClose, selectedLocation, onSuccess }: UploadModalProps) {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [files, setFiles] = useState<File[]>([])
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  if (!isOpen) return null

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || [])
    const validFiles = selectedFiles.filter(file => {
      const isImage = file.type.startsWith('image/')
      const isVideo = file.type.startsWith('video/')
      const isValidSize = file.size <= 50 * 1024 * 1024 // 50MB limit
      return (isImage || isVideo) && isValidSize
    })
    setFiles(validFiles)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedLocation || !title.trim() || !email.trim()) {
      setError('Please fill in all required fields and select a location on the map.')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      // Create a temporary user ID
      const tempUserId = crypto.randomUUID()

      // Create tornado report
      const newReport = localDB.addReport({
        lat: selectedLocation.lat,
        lng: selectedLocation.lng,
        title: title.trim(),
        description: description.trim(),
        user_id: tempUserId,
        user_email: email.trim(),
        status: 'pending'
      })

      // Handle file uploads (convert to base64 for local storage)
      if (files.length > 0) {
        for (const file of files) {
          // Convert file to base64 for local storage
          const base64 = await fileToBase64(file)

          // Add media record
          localDB.addMedia({
            report_id: newReport.id,
            file_url: base64,
            file_type: file.type.startsWith('image/') ? 'image' : 'video',
            user_id: tempUserId,
            status: 'pending'
          })
        }
      }

      // Reset form
      setTitle('')
      setDescription('')
      setFiles([])
      setEmail('')
      onSuccess()
      onClose()

      alert('Report submitted successfully! It will be reviewed before appearing on the map.')
    } catch (error) {
      console.error('Error submitting report:', error)
      setError('Failed to submit report. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Helper function to convert file to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = error => reject(error)
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-bold">Report Tornado Activity</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
            disabled={isSubmitting}
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Location
            </label>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <MapPin size={16} />
              {selectedLocation ? (
                <span>
                  {selectedLocation.lat.toFixed(4)}, {selectedLocation.lng.toFixed(4)}
                </span>
              ) : (
                <span className="text-red-500">Click on the map to select a location</span>
              )}
            </div>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Your Email *
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              required
              disabled={isSubmitting}
            />
          </div>

          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              Title *
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="e.g., Tornado spotted near downtown"
              required
              disabled={isSubmitting}
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
              placeholder="Describe what you observed..."
              disabled={isSubmitting}
            />
          </div>

          <div>
            <label htmlFor="files" className="block text-sm font-medium text-gray-700 mb-1">
              Photos/Videos (Optional)
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                id="files"
                multiple
                accept="image/*,video/*"
                onChange={handleFileChange}
                className="hidden"
                disabled={isSubmitting}
              />
              <label
                htmlFor="files"
                className="cursor-pointer flex flex-col items-center space-y-2"
              >
                <Upload size={24} className="text-gray-400" />
                <span className="text-sm text-gray-600">
                  Click to upload photos or videos
                </span>
                <span className="text-xs text-gray-500">
                  Max 50MB per file
                </span>
              </label>
            </div>
            {files.length > 0 && (
              <div className="mt-2 space-y-1">
                {files.map((file, index) => (
                  <div key={index} className="text-sm text-gray-600">
                    📎 {file.name} ({(file.size / 1024 / 1024).toFixed(1)}MB)
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
              disabled={isSubmitting || !selectedLocation}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Report'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
