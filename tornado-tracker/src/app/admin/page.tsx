'use client'

import { useState, useEffect } from 'react'
import { ArrowLeft, Check, X, Eye, Calendar, MapPin, Mail } from 'lucide-react'
import { localDB, TornadoReport, MediaUpload } from '@/lib/storage'
import Link from 'next/link'

export default function AdminPage() {
  const [reports, setReports] = useState<TornadoReport[]>([])
  const [mediaUploads, setMediaUploads] = useState<MediaUpload[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedReport, setSelectedReport] = useState<TornadoReport | null>(null)
  const [activeTab, setActiveTab] = useState<'pending' | 'approved' | 'rejected'>('pending')

  useEffect(() => {
    loadData()
  }, [activeTab])

  const loadData = () => {
    setLoading(true)
    try {
      // Load reports based on active tab
      const allReports = localDB.getReports()
      const filteredReports = allReports
        .filter(report => report.status === activeTab)
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

      // Load all media uploads
      const allMedia = localDB.getMedia()
      const sortedMedia = allMedia
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

      setReports(filteredReports)
      setMediaUploads(sortedMedia)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateReportStatus = (reportId: string, status: 'approved' | 'rejected') => {
    try {
      // Update report status
      localDB.updateReportStatus(reportId, status)

      // Also update associated media
      localDB.updateMediaStatus(reportId, status)

      loadData()
      setSelectedReport(null)
    } catch (error) {
      console.error('Error updating status:', error)
      alert('Failed to update status')
    }
  }

  const getReportMedia = (reportId: string) => {
    return mediaUploads.filter(media => media.report_id === reportId)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading reports...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
              >
                <ArrowLeft size={20} />
                <span>Back to Map</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8 w-fit">
          {(['pending', 'approved', 'rejected'] as const).map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 rounded-md font-medium capitalize transition-colors ${
                activeTab === tab
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab} ({reports.length})
            </button>
          ))}
        </div>

        {/* Reports Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {reports.map((report) => {
            const reportMedia = getReportMedia(report.id)
            return (
              <div
                key={report.id}
                className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="font-semibold text-lg text-gray-900 line-clamp-2">
                      {report.title}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      report.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      report.status === 'approved' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {report.status}
                    </span>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {report.description || 'No description provided'}
                  </p>

                  <div className="space-y-2 text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-2">
                      <MapPin size={14} />
                      <span>{report.lat.toFixed(4)}, {report.lng.toFixed(4)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar size={14} />
                      <span>{formatDate(report.created_at)}</span>
                    </div>
                    {report.user_email && (
                      <div className="flex items-center space-x-2">
                        <Mail size={14} />
                        <span className="truncate">{report.user_email}</span>
                      </div>
                    )}
                  </div>

                  {reportMedia.length > 0 && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700 mb-2">
                        Media ({reportMedia.length})
                      </p>
                      <div className="grid grid-cols-2 gap-2">
                        {reportMedia.slice(0, 4).map((media) => (
                          <div key={media.id} className="relative">
                            {media.file_type === 'image' ? (
                              <img
                                src={media.file_url}
                                alt="Report media"
                                className="w-full h-20 object-cover rounded"
                              />
                            ) : (
                              <div className="w-full h-20 bg-gray-100 rounded flex items-center justify-center">
                                <span className="text-xs text-gray-500">Video</span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedReport(report)}
                      className="flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center justify-center space-x-1"
                    >
                      <Eye size={16} />
                      <span>View</span>
                    </button>
                    {report.status === 'pending' && (
                      <>
                        <button
                          onClick={() => updateReportStatus(report.id, 'approved')}
                          className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center justify-center"
                        >
                          <Check size={16} />
                        </button>
                        <button
                          onClick={() => updateReportStatus(report.id, 'rejected')}
                          className="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center justify-center"
                        >
                          <X size={16} />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {reports.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🌪️</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No {activeTab} reports
            </h3>
            <p className="text-gray-500">
              {activeTab === 'pending' 
                ? 'All caught up! No reports waiting for review.'
                : `No reports have been ${activeTab} yet.`
              }
            </p>
          </div>
        )}
      </div>

      {/* Report Detail Modal */}
      {selectedReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-xl font-bold">Report Details</h2>
              <button
                onClick={() => setSelectedReport(null)}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6 space-y-6">
              <div>
                <h3 className="font-semibold text-lg mb-2">{selectedReport.title}</h3>
                <p className="text-gray-600">{selectedReport.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Location:</span>
                  <p>{selectedReport.lat.toFixed(6)}, {selectedReport.lng.toFixed(6)}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Submitted:</span>
                  <p>{formatDate(selectedReport.created_at)}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Status:</span>
                  <p className="capitalize">{selectedReport.status}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Reporter:</span>
                  <p>{selectedReport.user_email || 'Anonymous'}</p>
                </div>
              </div>

              {getReportMedia(selectedReport.id).length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-700 mb-3">Media Files</h4>
                  <div className="grid grid-cols-1 gap-4">
                    {getReportMedia(selectedReport.id).map((media) => (
                      <div key={media.id}>
                        {media.file_type === 'image' ? (
                          <img
                            src={media.file_url}
                            alt="Report media"
                            className="w-full rounded-lg"
                          />
                        ) : (
                          <video
                            src={media.file_url}
                            controls
                            className="w-full rounded-lg"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {selectedReport.status === 'pending' && (
                <div className="flex space-x-3 pt-4 border-t">
                  <button
                    onClick={() => updateReportStatus(selectedReport.id, 'approved')}
                    className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center justify-center space-x-2"
                  >
                    <Check size={20} />
                    <span>Approve</span>
                  </button>
                  <button
                    onClick={() => updateReportStatus(selectedReport.id, 'rejected')}
                    className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center justify-center space-x-2"
                  >
                    <X size={20} />
                    <span>Reject</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
