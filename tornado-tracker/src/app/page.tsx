'use client'

import { useState } from 'react'
import { Plus, Shield, AlertTriangle } from 'lucide-react'
import Map from '@/components/Map'
import UploadModal from '@/components/UploadModal'

export default function Home() {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [selectedLocation, setSelectedLocation] = useState<{ lat: number; lng: number } | null>(null)
  const [mapKey, setMapKey] = useState(0) // For refreshing map after new reports

  const handleLocationSelect = (lat: number, lng: number) => {
    setSelectedLocation({ lat, lng })
    setIsUploadModalOpen(true)
  }

  const handleUploadSuccess = () => {
    setMapKey(prev => prev + 1) // Refresh map to show new data
    setSelectedLocation(null)
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <header className="bg-red-600 text-white shadow-lg z-10">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="text-2xl">🌪️</div>
              <div>
                <h1 className="text-2xl font-bold">Tornado Tracker</h1>
                <p className="text-red-100 text-sm">America's Tornado Activity Monitor</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsUploadModalOpen(true)}
                className="bg-white text-red-600 px-4 py-2 rounded-lg font-semibold hover:bg-red-50 flex items-center space-x-2 transition-colors"
              >
                <Plus size={20} />
                <span>Report Tornado</span>
              </button>
              <a
                href="/admin"
                className="bg-red-700 text-white px-4 py-2 rounded-lg font-semibold hover:bg-red-800 flex items-center space-x-2 transition-colors"
              >
                <Shield size={20} />
                <span>Admin</span>
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* Info Banner */}
      <div className="bg-green-50 border-b border-green-200 px-4 py-2">
        <div className="container mx-auto flex items-center space-x-2 text-green-800">
          <AlertTriangle size={16} />
          <span className="text-sm">
            🎉 Now using open-source technologies! Click anywhere on the map to report tornado activity. All reports are stored locally and moderated before appearing publicly.
          </span>
        </div>
      </div>

      {/* Map Container */}
      <div className="flex-1 relative">
        <Map
          key={mapKey}
          onLocationSelect={handleLocationSelect}
          selectedLocation={selectedLocation}
        />
      </div>

      {/* Upload Modal */}
      <UploadModal
        isOpen={isUploadModalOpen}
        onClose={() => {
          setIsUploadModalOpen(false)
          setSelectedLocation(null)
        }}
        selectedLocation={selectedLocation}
        onSuccess={handleUploadSuccess}
      />

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-4">
        <div className="container mx-auto px-4 text-center">
          <p className="text-sm">
            Tornado Tracker - Helping communities stay informed about severe weather across America
          </p>
        </div>
      </footer>
    </div>
  )
}
