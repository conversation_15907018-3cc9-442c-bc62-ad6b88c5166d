// Local storage system to replace Supabase
// This uses localStorage for persistence and API routes for server-side operations

// Database types
export interface TornadoReport {
  id: string
  lat: number
  lng: number
  title: string
  description: string
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  user_id: string
  user_email?: string
}

export interface MediaUpload {
  id: string
  report_id: string
  file_url: string
  file_type: 'image' | 'video'
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  user_id: string
}

// Local storage keys
const REPORTS_KEY = 'tornado_reports'
const MEDIA_KEY = 'tornado_media'

// Helper functions for local storage
export const localDB = {
  // Get all reports
  getReports: (): TornadoReport[] => {
    if (typeof window === 'undefined') return []
    const stored = localStorage.getItem(REPORTS_KEY)
    return stored ? JSON.parse(stored) : []
  },

  // Save reports
  saveReports: (reports: TornadoReport[]) => {
    if (typeof window === 'undefined') return
    localStorage.setItem(REPORTS_KEY, JSON.stringify(reports))
  },

  // Add a new report
  addReport: (report: Omit<TornadoReport, 'id' | 'created_at'>) => {
    const reports = localDB.getReports()
    const newReport: TornadoReport = {
      ...report,
      id: crypto.randomUUID(),
      created_at: new Date().toISOString()
    }
    reports.push(newReport)
    localDB.saveReports(reports)
    return newReport
  },

  // Update report status
  updateReportStatus: (id: string, status: 'pending' | 'approved' | 'rejected') => {
    const reports = localDB.getReports()
    const reportIndex = reports.findIndex(r => r.id === id)
    if (reportIndex !== -1) {
      reports[reportIndex].status = status
      localDB.saveReports(reports)
      return reports[reportIndex]
    }
    return null
  },

  // Get media uploads
  getMedia: (): MediaUpload[] => {
    if (typeof window === 'undefined') return []
    const stored = localStorage.getItem(MEDIA_KEY)
    return stored ? JSON.parse(stored) : []
  },

  // Save media uploads
  saveMedia: (media: MediaUpload[]) => {
    if (typeof window === 'undefined') return
    localStorage.setItem(MEDIA_KEY, JSON.stringify(media))
  },

  // Add media upload
  addMedia: (media: Omit<MediaUpload, 'id' | 'created_at'>) => {
    const mediaList = localDB.getMedia()
    const newMedia: MediaUpload = {
      ...media,
      id: crypto.randomUUID(),
      created_at: new Date().toISOString()
    }
    mediaList.push(newMedia)
    localDB.saveMedia(mediaList)
    return newMedia
  },

  // Update media status
  updateMediaStatus: (reportId: string, status: 'pending' | 'approved' | 'rejected') => {
    const mediaList = localDB.getMedia()
    const updatedMedia = mediaList.map(m =>
      m.report_id === reportId ? { ...m, status } : m
    )
    localDB.saveMedia(updatedMedia)
    return updatedMedia.filter(m => m.report_id === reportId)
  }
}

// Initialize with some sample data if empty
export const initializeSampleData = () => {
  if (typeof window === 'undefined') return

  const reports = localDB.getReports()
  if (reports.length === 0) {
    // Add some sample tornado reports
    const sampleReports: TornadoReport[] = [
      {
        id: '1',
        lat: 39.7392,
        lng: -104.9903,
        title: 'Tornado Spotted Near Denver',
        description: 'Large tornado observed moving northeast of Denver metropolitan area. Significant debris visible.',
        status: 'approved',
        created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        user_id: 'sample-user-1',
        user_email: '<EMAIL>'
      },
      {
        id: '2',
        lat: 35.2271,
        lng: -101.8313,
        title: 'Tornado Activity in Texas Panhandle',
        description: 'Multiple tornadoes reported in the area. Emergency services responding.',
        status: 'approved',
        created_at: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        user_id: 'sample-user-2',
        user_email: '<EMAIL>'
      },
      {
        id: '3',
        lat: 38.2904,
        lng: -92.6390,
        title: 'Tornado Warning - Central Missouri',
        description: 'Funnel cloud developing into tornado. Residents advised to take shelter immediately.',
        status: 'pending',
        created_at: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        user_id: 'sample-user-3',
        user_email: '<EMAIL>'
      }
    ]
    localDB.saveReports(sampleReports)
  }
}
